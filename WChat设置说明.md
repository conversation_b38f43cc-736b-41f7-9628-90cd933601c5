# WChat设置功能说明

## 概述
已成功在WanTinyUI的第三个标签页中添加了WChat聊天增强模块的设置界面。

## 实现的功能

### 1. 标签页更新
- 第三个标签页按钮文字：从"占位" → "WChat设置"
- 标签页标题：从"占位" → "WChat聊天增强"
- 标签页配置：从CreateWanMenuPanel → CreateWChatPanel

### 2. WChat设置面板内容

#### 基础聊天设置
- **频道精简** (ShortChannel): 简化频道标签显示
- **表情输入** (EnableEmoteInput): 启用聊天表情功能
- **时间戳复制** (EnableTimestampCopy): 启用时间戳点击复制功能
- **垂直布局** (UseVertical): 聊天条使用垂直布局
- **聊天条在上方** (UseTopChatbar): 将聊天条放置在聊天框上方
- **输入框在上方** (UseTopInput): 将输入框放置在聊天框上方

#### 表情和透明度设置
- **表情大小** (EmoteIconSize): 聊天中表情图标的大小 (12-32)
- **列表表情** (EmoteIconListSize): 表情选择列表中图标的大小 (16-40)
- **悬停透明** (AlphaOnEnter): 鼠标悬停时的透明度 (0.1-1.0)
- **离开透明** (AlphaOnLeave): 鼠标离开时的透明度 (0.1-1.0)

#### 插件按钮显示设置
- **BiaoGe按钮** (ShowBiaoGeButton): 显示BiaoGe金团按钮
- **BiaoGeAI按钮** (ShowBiaoGeAIButton): 显示BiaoGeAI按钮
- **Atlas按钮** (ShowAtlasButton): 显示Atlas掉落按钮
- **集结号按钮** (ShowMeetingHornButton): 显示MeetingHorn按钮

#### 位置和间距设置
- **聊天条X** (ChatBarOffsetX): 聊天条水平偏移 (-200到200)
- **聊天条Y** (ChatBarOffsetY): 聊天条垂直偏移 (-200到200)
- **表情面板X** (EmoteOffsetX): 表情面板水平偏移 (-100到100)
- **表情面板Y** (EmoteOffsetY): 表情面板垂直偏移 (-100到100)
- **水平间距** (DistanceHorizontal): 聊天条按钮水平间距 (10-50)
- **垂直间距** (DistanceVertical): 聊天条按钮垂直间距 (-50到-10)

#### 操作按钮
- **刷新聊天条**: 立即刷新聊天条显示
- **恢复默认**: 将所有设置恢复为默认值

### 3. 技术实现特点

#### 简单的逻辑设计
- 使用最简单的配置读取和保存方式
- 直接操作 `_G.WChat_Config` 全局变量
- 调用现有的 `WChat.SaveConfig()` 和 `WChat.RefreshChatBarButtons()` 函数

#### 模块检测
- 添加了WChat模块的加载检测：`_G.WChat_Config ~= nil`
- 在模块未加载时显示友好的提示信息

#### 实时更新
- 插件按钮设置变更后自动刷新聊天条
- 位置和间距设置变更后实时更新显示

#### 布局设计
- 使用现有的WanTinyUI布局系统
- 复选框采用4列网格布局
- 滑块采用2列网格布局
- 保持与其他标签页一致的视觉风格

## 使用方法
1. 打开WanTinyUI主界面 (`/wt`)
2. 点击第三个标签页 "WChat设置"
3. 根据需要调整各项设置
4. 设置会自动保存，部分设置会实时生效
5. 如需恢复默认设置，点击"恢复默认"按钮

## 注意事项
- 需要WChat模块已加载才能显示设置界面
- 某些设置可能需要重载界面才能完全生效
- 插件按钮的显示还依赖于对应插件是否已加载
